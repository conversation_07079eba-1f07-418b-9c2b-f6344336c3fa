<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MataKuliah extends Model
{
    use HasFactory;

    protected $table = 'mata_kuliah';

    protected $fillable = [
        'kode_mk',
        'nama_mk',
        'jurusan',
        'sks',
        'deskripsi',
        'semester',
        'tahun_akademik',
    ];

    /**
     * Get materi kuliah for this mata kuliah
     */
    public function materiKuliah()
    {
        return $this->hasMany(MateriKuliah::class);
    }
}
