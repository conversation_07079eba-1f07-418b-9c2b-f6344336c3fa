<?php

use Illuminate\Support\Facades\Route;
use Livewire\Volt\Volt;

Route::get('/', function () {
    return view('welcome');
})->name('home');

// Dashboard routes with role-based access
Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        if (auth()->user()->isDosen()) {
            return redirect()->route('dosen.dashboard');
        } else {
            return redirect()->route('mahasiswa.dashboard');
        }
    })->name('dashboard');
});

// Mahasiswa routes
Route::middleware(['auth', 'role:mahasiswa'])->prefix('mahasiswa')->name('mahasiswa.')->group(function () {
    Route::view('dashboard', 'mahasiswa.dashboard')->name('dashboard');
    Route::view('materi', 'mahasiswa.materi')->name('materi');
    Route::view('jadwal', 'mahasiswa.jadwal')->name('jadwal');
    Route::view('notifikasi', 'mahasiswa.notifikasi')->name('notifikasi');
});

// Dosen routes
Route::middleware(['auth', 'role:dosen'])->prefix('dosen')->name('dosen.')->group(function () {
    Route::view('dashboard', 'dosen.dashboard')->name('dashboard');
    Route::view('materi', 'dosen.materi')->name('materi');
    Route::view('jadwal', 'dosen.jadwal')->name('jadwal');
    Route::view('upload-materi', 'dosen.upload-materi')->name('upload-materi');
});

Route::middleware(['auth'])->group(function () {
    Route::redirect('settings', 'settings/profile');

    Volt::route('settings/profile', 'settings.profile')->name('settings.profile');
    Volt::route('settings/password', 'settings.password')->name('settings.password');
    Volt::route('settings/appearance', 'settings.appearance')->name('settings.appearance');
});

require __DIR__.'/auth.php';
