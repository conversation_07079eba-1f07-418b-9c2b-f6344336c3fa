<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\MataKuliah;
use App\Models\MateriKuliah;
use App\Models\JadwalKuliah;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create sample users
        $dosen1 = User::create([
            'name' => 'Dr. Ahmad Wijaya',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'dosen',
            'nim_nip' => '198501012010011001',
            'jurusan' => 'Teknik Informatika',
            'phone' => '081234567890',
        ]);

        $dosen2 = User::create([
            'name' => 'Prof. Siti Nurhaliza',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'dosen',
            'nim_nip' => '197803152005012002',
            'jurusan' => 'Sistem Informasi',
            'phone' => '081234567891',
        ]);

        $mahasiswa1 = User::create([
            'name' => 'Budi Santoso',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'mahasiswa',
            'nim_nip' => '2021001001',
            'jurusan' => 'Teknik Informatika',
            'phone' => '081234567892',
        ]);

        $mahasiswa2 = User::create([
            'name' => 'Andi Pratama',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'mahasiswa',
            'nim_nip' => '2021001002',
            'jurusan' => 'Sistem Informasi',
            'phone' => '081234567893',
        ]);

        // Create sample mata kuliah
        $mataKuliah1 = MataKuliah::create([
            'kode_mk' => 'TIF101',
            'nama_mk' => 'Pemrograman Web',
            'jurusan' => 'Teknik Informatika',
            'sks' => 3,
            'deskripsi' => 'Mata kuliah yang membahas tentang pengembangan aplikasi web',
            'semester' => 'Ganjil',
            'tahun_akademik' => '2024/2025',
        ]);

        $mataKuliah2 = MataKuliah::create([
            'kode_mk' => 'SIF201',
            'nama_mk' => 'Basis Data',
            'jurusan' => 'Sistem Informasi',
            'sks' => 3,
            'deskripsi' => 'Mata kuliah yang membahas tentang perancangan dan implementasi basis data',
            'semester' => 'Genap',
            'tahun_akademik' => '2024/2025',
        ]);

        // Create sample materi kuliah
        MateriKuliah::create([
            'mata_kuliah_id' => $mataKuliah1->id,
            'dosen_id' => $dosen1->id,
            'judul' => 'Pengenalan HTML dan CSS',
            'deskripsi' => 'Materi pengenalan dasar HTML dan CSS untuk pemrograman web',
            'tipe' => 'text',
            'konten' => 'HTML (HyperText Markup Language) adalah bahasa markup standar untuk membuat halaman web...',
            'urutan' => 1,
        ]);

        MateriKuliah::create([
            'mata_kuliah_id' => $mataKuliah2->id,
            'dosen_id' => $dosen2->id,
            'judul' => 'Konsep Dasar Database',
            'deskripsi' => 'Pengenalan konsep dasar sistem basis data',
            'tipe' => 'link',
            'link_url' => 'https://example.com/database-basics',
            'urutan' => 1,
        ]);

        // Create sample jadwal kuliah
        JadwalKuliah::create([
            'kode_mk' => 'TIF101',
            'nama_mk' => 'Pemrograman Web',
            'jurusan' => 'Teknik Informatika',
            'tahun_akademik' => '2024/2025',
            'semester' => 'Ganjil',
            'nama_dosen' => 'Dr. Ahmad Wijaya',
            'ruang' => 'Lab Komputer 1',
            'hari' => 'Senin',
            'jam_mulai' => '08:00',
            'jam_selesai' => '10:30',
        ]);

        JadwalKuliah::create([
            'kode_mk' => 'SIF201',
            'nama_mk' => 'Basis Data',
            'jurusan' => 'Sistem Informasi',
            'tahun_akademik' => '2024/2025',
            'semester' => 'Genap',
            'nama_dosen' => 'Prof. Siti Nurhaliza',
            'ruang' => 'Ruang Kelas A201',
            'hari' => 'Rabu',
            'jam_mulai' => '13:00',
            'jam_selesai' => '15:30',
        ]);
    }
}
