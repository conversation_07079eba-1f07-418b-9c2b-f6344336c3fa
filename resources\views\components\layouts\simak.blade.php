<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="dark">
    <head>
        @include('partials.head')
        <style>
            :root {
                --primary-purple: #8b5cf6;
                --primary-pink: #ec4899;
                --gradient-bg: linear-gradient(135deg, #8b5cf6 0%, #ec4899 100%);
                --sidebar-bg: linear-gradient(180deg, #1e1b4b 0%, #581c87 100%);
            }
            
            .simak-gradient {
                background: var(--gradient-bg);
            }
            
            .simak-sidebar {
                background: var(--sidebar-bg);
            }
            
            .simak-card {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            .simak-text-gradient {
                background: var(--gradient-bg);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        </style>
    </head>
    <body class="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-pink-800">
        <flux:sidebar sticky stashable class="simak-sidebar border-e border-purple-600/30">
            <flux:sidebar.toggle class="lg:hidden text-white" icon="x-mark" />

            <div class="flex items-center space-x-3 mb-8 px-4">
                <div class="w-10 h-10 rounded-lg simak-gradient flex items-center justify-center">
                    <span class="text-white font-bold text-lg">S</span>
                </div>
                <div>
                    <h1 class="text-white font-bold text-xl">SIMAK</h1>
                    <p class="text-purple-200 text-xs">Sistem Informasi Materi Kuliah</p>
                </div>
            </div>

            <flux:navlist variant="outline" class="space-y-2">
                @if(auth()->user()->isDosen())
                    <flux:navlist.group :heading="__('Dosen Panel')" class="text-purple-200">
                        <flux:navlist.item 
                            icon="home" 
                            :href="route('dosen.dashboard')" 
                            :current="request()->routeIs('dosen.dashboard')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Dashboard') }}
                        </flux:navlist.item>
                        
                        <flux:navlist.item 
                            icon="book-open" 
                            :href="route('dosen.materi')" 
                            :current="request()->routeIs('dosen.materi')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Materi Kuliah') }}
                        </flux:navlist.item>
                        
                        <flux:navlist.item 
                            icon="upload" 
                            :href="route('dosen.upload-materi')" 
                            :current="request()->routeIs('dosen.upload-materi')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Upload Materi') }}
                        </flux:navlist.item>
                        
                        <flux:navlist.item 
                            icon="calendar" 
                            :href="route('dosen.jadwal')" 
                            :current="request()->routeIs('dosen.jadwal')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Jadwal Kuliah') }}
                        </flux:navlist.item>
                    </flux:navlist.group>
                @else
                    <flux:navlist.group :heading="__('Mahasiswa Panel')" class="text-purple-200">
                        <flux:navlist.item 
                            icon="home" 
                            :href="route('mahasiswa.dashboard')" 
                            :current="request()->routeIs('mahasiswa.dashboard')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Dashboard') }}
                        </flux:navlist.item>
                        
                        <flux:navlist.item 
                            icon="book-open" 
                            :href="route('mahasiswa.materi')" 
                            :current="request()->routeIs('mahasiswa.materi')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Materi Kuliah') }}
                        </flux:navlist.item>
                        
                        <flux:navlist.item 
                            icon="calendar" 
                            :href="route('mahasiswa.jadwal')" 
                            :current="request()->routeIs('mahasiswa.jadwal')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Jadwal Kuliah') }}
                        </flux:navlist.item>
                        
                        <flux:navlist.item 
                            icon="bell" 
                            :href="route('mahasiswa.notifikasi')" 
                            :current="request()->routeIs('mahasiswa.notifikasi')" 
                            wire:navigate
                            class="text-white hover:bg-purple-700/50 rounded-lg"
                        >
                            {{ __('Notifikasi') }}
                        </flux:navlist.item>
                    </flux:navlist.group>
                @endif
            </flux:navlist>

            <flux:spacer />

            <!-- Desktop User Menu -->
            <flux:dropdown class="hidden lg:block" position="bottom" align="start">
                <div class="flex items-center space-x-3 p-3 rounded-lg bg-purple-700/30 hover:bg-purple-700/50 transition-colors">
                    <div class="w-10 h-10 rounded-full simak-gradient flex items-center justify-center">
                        <span class="text-white font-semibold text-sm">{{ auth()->user()->initials() }}</span>
                    </div>
                    <div class="flex-1">
                        <p class="text-white font-medium text-sm">{{ auth()->user()->name }}</p>
                        <p class="text-purple-200 text-xs">{{ ucfirst(auth()->user()->role) }}</p>
                    </div>
                    <flux:icon name="chevrons-up-down" class="text-purple-200" />
                </div>

                <flux:menu class="w-[220px] bg-white/95 backdrop-blur-sm">
                    <div class="p-3 border-b border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full simak-gradient flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">{{ auth()->user()->initials() }}</span>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-900">{{ auth()->user()->name }}</p>
                                <p class="text-sm text-gray-600">{{ auth()->user()->email }}</p>
                                <p class="text-xs text-purple-600">{{ ucfirst(auth()->user()->role) }}</p>
                            </div>
                        </div>
                    </div>

                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate class="text-gray-700 hover:bg-purple-50">
                        {{ __('Settings') }}
                    </flux:menu.item>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full text-red-600 hover:bg-red-50">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:sidebar>

        <!-- Mobile Header -->
        <flux:header class="lg:hidden bg-gradient-to-r from-purple-600 to-pink-600 border-b border-purple-500/30">
            <flux:sidebar.toggle class="lg:hidden text-white" icon="bars-2" inset="left" />
            
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 rounded-lg bg-white/20 flex items-center justify-center">
                    <span class="text-white font-bold">S</span>
                </div>
                <span class="text-white font-bold">SIMAK</span>
            </div>

            <flux:spacer />

            <flux:dropdown position="top" align="end">
                <div class="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                    <span class="text-white font-semibold text-sm">{{ auth()->user()->initials() }}</span>
                </div>

                <flux:menu class="bg-white/95 backdrop-blur-sm">
                    <div class="p-3 border-b border-gray-200">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 rounded-full simak-gradient flex items-center justify-center">
                                <span class="text-white font-semibold text-sm">{{ auth()->user()->initials() }}</span>
                            </div>
                            <div>
                                <p class="font-semibold text-gray-900">{{ auth()->user()->name }}</p>
                                <p class="text-sm text-gray-600">{{ auth()->user()->email }}</p>
                                <p class="text-xs text-purple-600">{{ ucfirst(auth()->user()->role) }}</p>
                            </div>
                        </div>
                    </div>

                    <flux:menu.item :href="route('settings.profile')" icon="cog" wire:navigate class="text-gray-700 hover:bg-purple-50">
                        {{ __('Settings') }}
                    </flux:menu.item>

                    <flux:menu.separator />

                    <form method="POST" action="{{ route('logout') }}" class="w-full">
                        @csrf
                        <flux:menu.item as="button" type="submit" icon="arrow-right-start-on-rectangle" class="w-full text-red-600 hover:bg-red-50">
                            {{ __('Log Out') }}
                        </flux:menu.item>
                    </form>
                </flux:menu>
            </flux:dropdown>
        </flux:header>

        <flux:main class="bg-transparent">
            <div class="min-h-screen bg-gradient-to-br from-purple-50/50 to-pink-50/50 backdrop-blur-sm">
                {{ $slot }}
            </div>
        </flux:main>

        @fluxScripts
    </body>
</html>
