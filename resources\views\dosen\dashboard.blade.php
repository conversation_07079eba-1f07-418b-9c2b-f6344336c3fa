<x-layouts.simak>
    <div class="p-6 space-y-6">
        <!-- Welcome Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        Selamat Datang, <span class="simak-text-gradient">{{ auth()->user()->name }}</span>
                    </h1>
                    <p class="text-gray-600 mt-1">{{ auth()->user()->nim_nip }} - {{ auth()->user()->jurus<PERSON> }}</p>
                    <p class="text-sm text-purple-600 mt-2">Dashboard Dosen SIMAK</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-20 h-20 rounded-2xl simak-gradient flex items-center justify-center">
                        <flux:icon name="user-group" class="w-10 h-10 text-white" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Materi -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-purple-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Materi Diupload</p>
                        <p class="text-3xl font-bold text-purple-600">12</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                        <flux:icon name="document-text" class="w-6 h-6 text-purple-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600 font-medium">+2 minggu ini</span>
                </div>
            </div>

            <!-- Mata Kuliah -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Mata Kuliah</p>
                        <p class="text-3xl font-bold text-pink-600">4</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-pink-100 flex items-center justify-center">
                        <flux:icon name="book-open" class="w-6 h-6 text-pink-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-blue-600 font-medium">Semester ini</span>
                </div>
            </div>

            <!-- Total Komentar -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-orange-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Komentar</p>
                        <p class="text-3xl font-bold text-orange-600">47</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                        <flux:icon name="chat-bubble-left-right" class="w-6 h-6 text-orange-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-purple-600 font-medium">15 belum dibalas</span>
                </div>
            </div>

            <!-- Jadwal Hari Ini -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-blue-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Jadwal Hari Ini</p>
                        <p class="text-3xl font-bold text-blue-600">2</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                        <flux:icon name="calendar" class="w-6 h-6 text-blue-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600 font-medium">Mulai 08:00</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Aksi Cepat</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="{{ route('dosen.upload-materi') }}" class="flex flex-col items-center p-4 rounded-xl bg-purple-50 hover:bg-purple-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg simak-gradient flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="upload" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Upload Materi</span>
                </a>

                <a href="{{ route('dosen.materi') }}" class="flex flex-col items-center p-4 rounded-xl bg-pink-50 hover:bg-pink-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="document-text" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Kelola Materi</span>
                </a>

                <a href="{{ route('dosen.jadwal') }}" class="flex flex-col items-center p-4 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="calendar" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Jadwal Kuliah</span>
                </a>

                <a href="#" class="flex flex-col items-center p-4 rounded-xl bg-green-50 hover:bg-green-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-r from-green-500 to-purple-500 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="chart-bar" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Statistik</span>
                </a>
            </div>
        </div>

        <!-- Recent Activity & Schedule -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Activity -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Aktivitas Terbaru</h2>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                            <flux:icon name="upload" class="w-4 h-4 text-purple-600" />
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Materi berhasil diupload</p>
                            <p class="text-xs text-gray-600">Pemrograman Web - JavaScript Dasar</p>
                            <p class="text-xs text-purple-600">1 jam yang lalu</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center flex-shrink-0">
                            <flux:icon name="chat-bubble-left" class="w-4 h-4 text-pink-600" />
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Komentar baru dari mahasiswa</p>
                            <p class="text-xs text-gray-600">Budi Santoso - Pertanyaan tentang Array</p>
                            <p class="text-xs text-purple-600">3 jam yang lalu</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <flux:icon name="pencil-square" class="w-4 h-4 text-blue-600" />
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Materi diperbarui</p>
                            <p class="text-xs text-gray-600">Basis Data - Konsep Normalisasi</p>
                            <p class="text-xs text-purple-600">1 hari yang lalu</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Schedule -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-200/50 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Jadwal Mengajar Hari Ini</h2>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4 p-3 rounded-lg bg-purple-50">
                        <div class="text-center">
                            <p class="text-sm font-bold text-purple-600">08:00</p>
                            <p class="text-xs text-purple-500">10:30</p>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">Pemrograman Web</p>
                            <p class="text-sm text-gray-600">TIF101 - Teknik Informatika</p>
                            <p class="text-xs text-purple-600">Lab Komputer 1</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 p-3 rounded-lg bg-pink-50">
                        <div class="text-center">
                            <p class="text-sm font-bold text-pink-600">13:00</p>
                            <p class="text-xs text-pink-500">15:30</p>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">Algoritma Pemrograman</p>
                            <p class="text-sm text-gray-600">TIF102 - Teknik Informatika</p>
                            <p class="text-xs text-pink-600">Ruang B102</p>
                        </div>
                    </div>
                </div>

                <div class="mt-6 pt-4 border-t border-gray-200">
                    <h3 class="font-medium text-gray-900 mb-3">Komentar Menunggu Balasan</h3>
                    <div class="space-y-2">
                        <div class="flex items-center justify-between p-2 rounded-lg bg-orange-50">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Budi Santoso</p>
                                <p class="text-xs text-gray-600">Pertanyaan tentang Array</p>
                            </div>
                            <span class="text-xs text-orange-600 font-medium">3 jam</span>
                        </div>
                        <div class="flex items-center justify-between p-2 rounded-lg bg-orange-50">
                            <div>
                                <p class="text-sm font-medium text-gray-900">Andi Pratama</p>
                                <p class="text-xs text-gray-600">Konsep OOP</p>
                            </div>
                            <span class="text-xs text-orange-600 font-medium">5 jam</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.simak>
