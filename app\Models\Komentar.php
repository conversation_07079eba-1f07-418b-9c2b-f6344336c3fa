<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Komentar extends Model
{
    use HasFactory;

    protected $table = 'komentar';

    protected $fillable = [
        'materi_kuliah_id',
        'user_id',
        'komentar',
        'parent_id',
    ];

    /**
     * Get materi kuliah that owns this komentar
     */
    public function materiKuliah()
    {
        return $this->belongsTo(MateriKuliah::class);
    }

    /**
     * Get user that made this komentar
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get parent komentar (for replies)
     */
    public function parent()
    {
        return $this->belongsTo(Komentar::class, 'parent_id');
    }

    /**
     * Get replies to this komentar
     */
    public function replies()
    {
        return $this->hasMany(Komentar::class, 'parent_id');
    }
}
