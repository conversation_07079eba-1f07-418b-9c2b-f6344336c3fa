<x-layouts.simak>
    <div class="p-6 space-y-6">
        <!-- Welcome Header -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">
                        Selamat Datang, <span class="simak-text-gradient">{{ auth()->user()->name }}</span>
                    </h1>
                    <p class="text-gray-600 mt-1">{{ auth()->user()->nim_nip }} - {{ auth()->user()->jurus<PERSON> }}</p>
                    <p class="text-sm text-purple-600 mt-2">Dashboard Mahasiswa SIMAK</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-20 h-20 rounded-2xl simak-gradient flex items-center justify-center">
                        <flux:icon name="academic-cap" class="w-10 h-10 text-white" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Materi -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-purple-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Materi</p>
                        <p class="text-3xl font-bold text-purple-600">24</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center">
                        <flux:icon name="book-open" class="w-6 h-6 text-purple-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-green-600 font-medium">+3 materi baru</span>
                </div>
            </div>

            <!-- Jadwal Hari Ini -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-pink-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Jadwal Hari Ini</p>
                        <p class="text-3xl font-bold text-pink-600">3</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-pink-100 flex items-center justify-center">
                        <flux:icon name="calendar" class="w-6 h-6 text-pink-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-blue-600 font-medium">Mulai 08:00</span>
                </div>
            </div>

            <!-- Tugas Pending -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-orange-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Tugas Pending</p>
                        <p class="text-3xl font-bold text-orange-600">5</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-orange-100 flex items-center justify-center">
                        <flux:icon name="clipboard-document-list" class="w-6 h-6 text-orange-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-red-600 font-medium">2 deadline dekat</span>
                </div>
            </div>

            <!-- Notifikasi -->
            <div class="bg-white/80 backdrop-blur-sm rounded-xl p-6 border border-blue-200/50 shadow-lg hover:shadow-xl transition-shadow">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Notifikasi</p>
                        <p class="text-3xl font-bold text-blue-600">8</p>
                    </div>
                    <div class="w-12 h-12 rounded-lg bg-blue-100 flex items-center justify-center">
                        <flux:icon name="bell" class="w-6 h-6 text-blue-600" />
                    </div>
                </div>
                <div class="mt-4">
                    <span class="text-sm text-purple-600 font-medium">3 belum dibaca</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
            <h2 class="text-xl font-bold text-gray-900 mb-4">Aksi Cepat</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="{{ route('mahasiswa.materi') }}" class="flex flex-col items-center p-4 rounded-xl bg-purple-50 hover:bg-purple-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg simak-gradient flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="book-open" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Lihat Materi</span>
                </a>

                <a href="{{ route('mahasiswa.jadwal') }}" class="flex flex-col items-center p-4 rounded-xl bg-pink-50 hover:bg-pink-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-r from-pink-500 to-purple-500 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="calendar" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Jadwal Kuliah</span>
                </a>

                <a href="{{ route('mahasiswa.notifikasi') }}" class="flex flex-col items-center p-4 rounded-xl bg-blue-50 hover:bg-blue-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="bell" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Notifikasi</span>
                </a>

                <a href="#" class="flex flex-col items-center p-4 rounded-xl bg-green-50 hover:bg-green-100 transition-colors group">
                    <div class="w-12 h-12 rounded-lg bg-gradient-to-r from-green-500 to-purple-500 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform">
                        <flux:icon name="magnifying-glass" class="w-6 h-6 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">Cari Materi</span>
                </a>
            </div>
        </div>

        <!-- Recent Activity & Schedule -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Activity -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Aktivitas Terbaru</h2>
                <div class="space-y-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center flex-shrink-0">
                            <flux:icon name="book-open" class="w-4 h-4 text-purple-600" />
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Materi baru ditambahkan</p>
                            <p class="text-xs text-gray-600">Pemrograman Web - HTML & CSS</p>
                            <p class="text-xs text-purple-600">2 jam yang lalu</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-pink-100 flex items-center justify-center flex-shrink-0">
                            <flux:icon name="chat-bubble-left" class="w-4 h-4 text-pink-600" />
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Komentar baru</p>
                            <p class="text-xs text-gray-600">Basis Data - Konsep Normalisasi</p>
                            <p class="text-xs text-purple-600">5 jam yang lalu</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-3">
                        <div class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                            <flux:icon name="bell" class="w-4 h-4 text-blue-600" />
                        </div>
                        <div class="flex-1">
                            <p class="text-sm font-medium text-gray-900">Pengingat jadwal</p>
                            <p class="text-xs text-gray-600">Kuliah Algoritma besok pagi</p>
                            <p class="text-xs text-purple-600">1 hari yang lalu</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Today's Schedule -->
            <div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-pink-200/50 shadow-lg">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Jadwal Hari Ini</h2>
                <div class="space-y-4">
                    <div class="flex items-center space-x-4 p-3 rounded-lg bg-purple-50">
                        <div class="text-center">
                            <p class="text-sm font-bold text-purple-600">08:00</p>
                            <p class="text-xs text-purple-500">10:30</p>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">Pemrograman Web</p>
                            <p class="text-sm text-gray-600">Dr. Ahmad Wijaya</p>
                            <p class="text-xs text-purple-600">Lab Komputer 1</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 p-3 rounded-lg bg-pink-50">
                        <div class="text-center">
                            <p class="text-sm font-bold text-pink-600">13:00</p>
                            <p class="text-xs text-pink-500">15:30</p>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">Basis Data</p>
                            <p class="text-sm text-gray-600">Prof. Siti Nurhaliza</p>
                            <p class="text-xs text-pink-600">Ruang A201</p>
                        </div>
                    </div>

                    <div class="flex items-center space-x-4 p-3 rounded-lg bg-blue-50">
                        <div class="text-center">
                            <p class="text-sm font-bold text-blue-600">15:45</p>
                            <p class="text-xs text-blue-500">17:15</p>
                        </div>
                        <div class="flex-1">
                            <p class="font-medium text-gray-900">Algoritma</p>
                            <p class="text-sm text-gray-600">Dr. Budi Hartono</p>
                            <p class="text-xs text-blue-600">Ruang B102</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-layouts.simak>
