<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class MateriKuliah extends Model
{
    use HasFactory;

    protected $table = 'materi_kuliah';

    protected $fillable = [
        'mata_kuliah_id',
        'dosen_id',
        'judul',
        'deskripsi',
        'tipe',
        'konten',
        'file_path',
        'link_url',
        'urutan',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get mata kuliah that owns this materi
     */
    public function mataKuliah()
    {
        return $this->belongsTo(MataKuliah::class);
    }

    /**
     * Get dosen that uploaded this materi
     */
    public function dosen()
    {
        return $this->belongsTo(User::class, 'dosen_id');
    }

    /**
     * Get komentar for this materi
     */
    public function komentar()
    {
        return $this->hasMany(Komentar::class);
    }
}
